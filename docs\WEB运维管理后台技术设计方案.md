# WEB运维管理后台技术设计方案

## 📋 项目概述

基于现有实时公交系统，设计和开发一个功能完善的WEB运维管理后台，为系统运营提供全面的数据管理、实时监控、系统维护等功能支撑。

## 🏗️ 技术架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    WEB运维管理后台                           │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue.js 3 + TypeScript)                            │
│  ├── 管理界面 (Element Plus)                                │
│  ├── 地图组件 (高德地图)                                     │
│  ├── 图表组件 (ECharts)                                     │
│  └── 实时通信 (SignalR Client)                              │
├─────────────────────────────────────────────────────────────┤
│  API网关层 (现有 BusSystem.Api 扩展)                        │
│  ├── 管理API控制器                                          │
│  ├── 认证授权中间件                                          │
│  ├── 权限控制中间件                                          │
│  └── 操作审计中间件                                          │
├─────────────────────────────────────────────────────────────┤
│  业务服务层 (现有 BusSystem.Core 扩展)                      │
│  ├── 管理服务 (AdminService)                               │
│  ├── 用户权限服务 (UserManagementService)                   │
│  ├── 数据维护服务 (DataMaintenanceService)                  │
│  └── 统计分析服务 (AnalyticsService)                        │
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (现有 BusSystem.Infrastructure 扩展)            │
│  ├── 管理数据仓储                                            │
│  ├── 用户权限仓储                                            │
│  └── 审计日志仓储                                            │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (现有数据库扩展)                                 │
│  ├── PostgreSQL (业务数据 + 管理数据)                       │
│  ├── TimescaleDB (时序数据 + 监控数据)                       │
│  └── Redis (缓存 + 会话)                                    │
└─────────────────────────────────────────────────────────────┘
```

### 前端技术栈
- **框架**: Vue.js 3.4+ (Composition API)
- **语言**: TypeScript 5.0+
- **构建工具**: Vite 5.0+
- **UI框架**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **地图**: 高德地图 JS API 2.0
- **图表**: Apache ECharts 5.4+
- **实时通信**: @microsoft/signalr 8.0+
- **工具库**: Lodash-es, Day.js, Crypto-js

### 后端技术扩展
- **框架**: 基于现有 .NET 8 API
- **认证**: JWT + Cookie 双重认证
- **授权**: 基于角色的访问控制 (RBAC)
- **缓存**: Redis 分布式缓存
- **日志**: Serilog 结构化日志
- **监控**: 自定义性能监控中间件

## 🗄️ 数据库设计扩展

### 管理相关表结构
```sql
-- 用户管理表
CREATE TABLE admin_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    full_name VARCHAR(100),
    role_id INTEGER REFERENCES admin_roles(id),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色管理表
CREATE TABLE admin_roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB, -- 权限列表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 操作审计日志表
CREATE TABLE admin_audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES admin_users(id),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(50),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB,
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    updated_by INTEGER REFERENCES admin_users(id),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据质量监控表
CREATE TABLE data_quality_metrics (
    id BIGSERIAL PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL, -- gps_quality, prediction_accuracy, etc.
    metric_value DECIMAL(10,4),
    details JSONB,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (recorded_at);

-- 任务调度表
CREATE TABLE scheduled_tasks (
    id SERIAL PRIMARY KEY,
    task_name VARCHAR(100) UNIQUE NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    cron_expression VARCHAR(100),
    is_enabled BOOLEAN DEFAULT true,
    last_run_at TIMESTAMP,
    next_run_at TIMESTAMP,
    run_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🎨 前端架构设计

### 项目结构
```
bus-admin-web/
├── public/                     # 静态资源
├── src/
│   ├── api/                   # API接口定义
│   │   ├── admin.ts          # 管理相关API
│   │   ├── monitoring.ts     # 监控相关API
│   │   ├── data.ts           # 数据管理API
│   │   └── analytics.ts      # 统计分析API
│   ├── components/           # 公共组件
│   │   ├── common/          # 通用组件
│   │   ├── charts/          # 图表组件
│   │   ├── maps/            # 地图组件
│   │   └── forms/           # 表单组件
│   ├── views/               # 页面组件
│   │   ├── dashboard/       # 仪表板
│   │   ├── data/           # 数据管理
│   │   ├── monitoring/     # 实时监控
│   │   ├── maintenance/    # 数据维护
│   │   ├── system/         # 系统管理
│   │   └── analytics/      # 统计分析
│   ├── stores/             # Pinia状态管理
│   ├── router/             # 路由配置
│   ├── utils/              # 工具函数
│   ├── types/              # TypeScript类型定义
│   └── styles/             # 样式文件
├── package.json
├── vite.config.ts
└── tsconfig.json
```

### 核心组件设计

#### 1. 地图组件 (MapComponent.vue)
```typescript
interface MapComponentProps {
  vehicles?: VehiclePosition[]
  routes?: BusRoute[]
  stops?: BusStop[]
  showTrajectory?: boolean
  showHeatmap?: boolean
  interactive?: boolean
}

interface MapComponentEmits {
  vehicleClick: (vehicle: VehiclePosition) => void
  stopClick: (stop: BusStop) => void
  routeClick: (route: BusRoute) => void
}
```

#### 2. 实时数据表格 (RealtimeTable.vue)
```typescript
interface RealtimeTableProps {
  dataSource: string // SignalR数据源
  columns: TableColumn[]
  filters?: FilterConfig[]
  pagination?: boolean
  autoRefresh?: boolean
}
```

#### 3. 图表组件 (ChartComponent.vue)
```typescript
interface ChartComponentProps {
  chartType: 'line' | 'bar' | 'pie' | 'heatmap' | 'gauge'
  data: ChartData
  options?: EChartsOption
  realtime?: boolean
  height?: string
}
```

### 状态管理设计

#### 用户状态 (useUserStore)
```typescript
export const useUserStore = defineStore('user', () => {
  const user = ref<AdminUser | null>(null)
  const permissions = ref<string[]>([])
  const token = ref<string>('')
  
  const login = async (credentials: LoginCredentials) => { /* ... */ }
  const logout = async () => { /* ... */ }
  const hasPermission = (permission: string) => { /* ... */ }
  
  return { user, permissions, token, login, logout, hasPermission }
})
```

#### 系统状态 (useSystemStore)
```typescript
export const useSystemStore = defineStore('system', () => {
  const systemHealth = ref<SystemHealth>()
  const notifications = ref<Notification[]>([])
  
  const fetchSystemHealth = async () => { /* ... */ }
  const addNotification = (notification: Notification) => { /* ... */ }
  
  return { systemHealth, notifications, fetchSystemHealth, addNotification }
})
```

## 🔧 后端架构扩展

### 管理API控制器

#### AdminController.cs
```csharp
[ApiController]
[Route("api/admin")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminController : ControllerBase
{
    // 系统概览
    [HttpGet("dashboard")]
    public async Task<ActionResult<DashboardData>> GetDashboard()
    
    // 系统健康检查
    [HttpGet("health")]
    public async Task<ActionResult<SystemHealth>> GetSystemHealth()
    
    // 用户管理
    [HttpGet("users")]
    public async Task<ActionResult<PagedResult<AdminUser>>> GetUsers()
    
    [HttpPost("users")]
    public async Task<ActionResult<AdminUser>> CreateUser()
}
```

#### DataManagementController.cs
```csharp
[ApiController]
[Route("api/admin/data")]
[Authorize(Roles = "Admin,DataManager")]
public class DataManagementController : ControllerBase
{
    // 线路管理
    [HttpGet("routes")]
    public async Task<ActionResult<PagedResult<BusRoute>>> GetRoutes()
    
    // 轨迹管理
    [HttpPost("routes/{id}/trajectory")]
    public async Task<ActionResult> UpdateTrajectory()
    
    // 站点管理
    [HttpGet("stops")]
    public async Task<ActionResult<PagedResult<BusStop>>> GetStops()
}
```

#### MonitoringController.cs
```csharp
[ApiController]
[Route("api/admin/monitoring")]
[Authorize(Roles = "Admin,Operator")]
public class MonitoringController : ControllerBase
{
    // 实时车辆监控
    [HttpGet("vehicles/realtime")]
    public async Task<ActionResult<List<VehicleRealtimeInfo>>> GetRealtimeVehicles()
    
    // 系统性能监控
    [HttpGet("performance")]
    public async Task<ActionResult<PerformanceMetrics>> GetPerformanceMetrics()
    
    // 数据质量监控
    [HttpGet("data-quality")]
    public async Task<ActionResult<DataQualityReport>> GetDataQuality()
}
```

### 业务服务扩展

#### AdminService.cs
```csharp
public interface IAdminService
{
    Task<DashboardData> GetDashboardDataAsync();
    Task<SystemHealth> GetSystemHealthAsync();
    Task<List<AdminUser>> GetUsersAsync();
    Task<AdminUser> CreateUserAsync(CreateUserRequest request);
    Task<bool> UpdateUserAsync(int userId, UpdateUserRequest request);
    Task<bool> DeleteUserAsync(int userId);
}
```

#### DataMaintenanceService.cs
```csharp
public interface IDataMaintenanceService
{
    Task<TrajectoryGenerationResult> GenerateRouteTrajectoryAsync(int routeId);
    Task<DataQualityReport> AnalyzeDataQualityAsync();
    Task<CleanupResult> CleanupHistoricalDataAsync(CleanupOptions options);
    Task<ValidationResult> ValidateRouteDataAsync(int routeId);
}
```

## 🔐 安全设计

### 认证授权
- **JWT Token**: API访问令牌，有效期2小时
- **Refresh Token**: 刷新令牌，有效期7天
- **Cookie Session**: 浏览器会话，用于记住登录状态
- **RBAC权限**: 基于角色的细粒度权限控制

### 权限定义
```csharp
public static class Permissions
{
    public const string ViewDashboard = "dashboard.view";
    public const string ManageUsers = "users.manage";
    public const string ManageData = "data.manage";
    public const string ViewMonitoring = "monitoring.view";
    public const string ManageSystem = "system.manage";
    public const string ViewAnalytics = "analytics.view";
}
```

### 操作审计
- 所有管理操作自动记录审计日志
- 包含操作用户、时间、IP、操作内容
- 支持审计日志查询和导出

## 📊 监控和日志

### 性能监控
- API响应时间监控
- 数据库查询性能监控
- 缓存命中率监控
- 系统资源使用监控

### 日志管理
- 结构化日志记录 (Serilog)
- 日志级别：Debug, Info, Warning, Error, Fatal
- 日志聚合和搜索
- 异常告警机制

## 🚀 部署架构

### 开发环境
- 前端：Vite Dev Server (端口3000)
- 后端：.NET 8 Kestrel (端口5000)
- 数据库：本地 PostgreSQL + Redis

### 生产环境
- 前端：Nginx 静态文件服务
- 后端：Docker 容器化部署
- 数据库：PostgreSQL 集群 + Redis 集群
- 负载均衡：Nginx 反向代理

这个技术设计方案为WEB运维管理后台提供了完整的架构指导，确保系统的可扩展性、安全性和性能。
